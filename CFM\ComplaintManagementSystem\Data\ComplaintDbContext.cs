using Microsoft.EntityFrameworkCore;
using ComplaintManagementSystem.Models;

namespace ComplaintManagementSystem.Data
{
    public class ComplaintDbContext : DbContext
    {
        public ComplaintDbContext(DbContextOptions<ComplaintDbContext> options) : base(options)
        {
        }

        public DbSet<Complaint> Complaints { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<ComplaintStatus> ComplaintStatuses { get; set; }
        public DbSet<ComplaintCategory> ComplaintCategories { get; set; }
        public DbSet<ComplaintPriority> ComplaintPriorities { get; set; }
        public DbSet<ComplaintHistory> ComplaintHistories { get; set; }
        public DbSet<ComplaintComment> ComplaintComments { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships
            modelBuilder.Entity<Complaint>()
                .HasOne(c => c.CreatedByUser)
                .WithMany(u => u.CreatedComplaints)
                .HasForeignKey(c => c.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Complaint>()
                .HasOne(c => c.AssignedToUser)
                .WithMany(u => u.AssignedComplaints)
                .HasForeignKey(c => c.AssignedToUserId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<ComplaintHistory>()
                .HasOne(ch => ch.OldAssignedToUser)
                .WithMany()
                .HasForeignKey(ch => ch.OldAssignedToUserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ComplaintHistory>()
                .HasOne(ch => ch.NewAssignedToUser)
                .WithMany()
                .HasForeignKey(ch => ch.NewAssignedToUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed ComplaintStatuses
            modelBuilder.Entity<ComplaintStatus>().HasData(
                new ComplaintStatus { Id = 1, Name = "جديدة", Description = "شكوى جديدة لم يتم التعامل معها", Color = "#2196F3", SortOrder = 1 },
                new ComplaintStatus { Id = 2, Name = "قيد المراجعة", Description = "شكوى قيد المراجعة", Color = "#FF9800", SortOrder = 2 },
                new ComplaintStatus { Id = 3, Name = "قيد التنفيذ", Description = "شكوى قيد التنفيذ", Color = "#9C27B0", SortOrder = 3 },
                new ComplaintStatus { Id = 4, Name = "مكتملة", Description = "شكوى مكتملة", Color = "#4CAF50", SortOrder = 4 },
                new ComplaintStatus { Id = 5, Name = "مرفوضة", Description = "شكوى مرفوضة", Color = "#F44336", SortOrder = 5 },
                new ComplaintStatus { Id = 6, Name = "مؤجلة", Description = "شكوى مؤجلة", Color = "#607D8B", SortOrder = 6 }
            );

            // Seed ComplaintCategories
            modelBuilder.Entity<ComplaintCategory>().HasData(
                new ComplaintCategory { Id = 1, Name = "خدمات عامة", Description = "شكاوى متعلقة بالخدمات العامة", Color = "#2196F3", SortOrder = 1 },
                new ComplaintCategory { Id = 2, Name = "موارد بشرية", Description = "شكاوى متعلقة بالموارد البشرية", Color = "#4CAF50", SortOrder = 2 },
                new ComplaintCategory { Id = 3, Name = "تقنية المعلومات", Description = "شكاوى متعلقة بتقنية المعلومات", Color = "#FF9800", SortOrder = 3 },
                new ComplaintCategory { Id = 4, Name = "مالية", Description = "شكاوى متعلقة بالأمور المالية", Color = "#9C27B0", SortOrder = 4 },
                new ComplaintCategory { Id = 5, Name = "أخرى", Description = "شكاوى أخرى", Color = "#607D8B", SortOrder = 5 }
            );

            // Seed ComplaintPriorities
            modelBuilder.Entity<ComplaintPriority>().HasData(
                new ComplaintPriority { Id = 1, Name = "منخفضة", Description = "أولوية منخفضة", Color = "#4CAF50", Level = 1, SortOrder = 1 },
                new ComplaintPriority { Id = 2, Name = "متوسطة", Description = "أولوية متوسطة", Color = "#FF9800", Level = 2, SortOrder = 2 },
                new ComplaintPriority { Id = 3, Name = "عالية", Description = "أولوية عالية", Color = "#F44336", Level = 3, SortOrder = 3 },
                new ComplaintPriority { Id = 4, Name = "عاجلة", Description = "أولوية عاجلة", Color = "#E91E63", Level = 4, SortOrder = 4 }
            );

            // Seed default admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    FullName = "مدير النظام",
                    Username = "admin",
                    Email = "<EMAIL>",
                    Department = "تقنية المعلومات",
                    Position = "مدير النظام",
                    Role = UserRole.Admin,
                    CreatedDate = new DateTime(2025, 1, 1)
                }
            );
        }
    }
}
