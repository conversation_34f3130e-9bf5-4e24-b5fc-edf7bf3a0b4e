using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComplaintManagementSystem.Models
{
    public class ComplaintHistory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ComplaintId { get; set; }

        [ForeignKey("ComplaintId")]
        public virtual Complaint Complaint { get; set; } = null!;

        [Required]
        [StringLength(200)]
        public string Action { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        public DateTime ActionDate { get; set; } = DateTime.Now;

        [Required]
        public int ActionByUserId { get; set; }

        [ForeignKey("ActionByUserId")]
        public virtual User ActionByUser { get; set; } = null!;

        public int? OldStatusId { get; set; }

        [ForeignKey("OldStatusId")]
        public virtual ComplaintStatus? OldStatus { get; set; }

        public int? NewStatusId { get; set; }

        [ForeignKey("NewStatusId")]
        public virtual ComplaintStatus? NewStatus { get; set; }

        public int? OldAssignedToUserId { get; set; }

        [ForeignKey("OldAssignedToUserId")]
        public virtual User? OldAssignedToUser { get; set; }

        public int? NewAssignedToUserId { get; set; }

        [ForeignKey("NewAssignedToUserId")]
        public virtual User? NewAssignedToUser { get; set; }
    }
}
