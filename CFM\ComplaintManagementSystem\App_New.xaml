<Application x:Class="ComplaintManagementSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ComplaintManagementSystem"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Modern Card Style -->
                    <Style x:Key="ModernCard" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="5"/>
                            </Setter.Value>
                        </Setter>
                    </Style>

                    <!-- Sidebar Button Style -->
                    <Style x:Key="SidebarButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                        <Setter Property="Height" Value="50"/>
                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                        <Setter Property="HorizontalContentAlignment" Value="Left"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="Margin" Value="4,2"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontFamily" Value="Segoe UI"/>
                        <Setter Property="Foreground" Value="#333333"/>
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <!-- Header Style -->
                    <Style x:Key="HeaderText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Foreground" Value="#1976D2"/>
                        <Setter Property="Margin" Value="0,0,0,16"/>
                    </Style>

                    <!-- Statistic Card Style -->
                    <Style x:Key="StatCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
                        <Setter Property="Width" Value="200"/>
                        <Setter Property="Height" Value="120"/>
                        <Setter Property="Cursor" Value="Hand"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <!-- RTL Support -->
                    <Style TargetType="Window">
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                    </Style>

                    <Style TargetType="UserControl">
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
