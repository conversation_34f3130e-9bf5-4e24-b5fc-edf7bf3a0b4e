<Window x:Class="ComplaintManagementSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="Complaint Management System" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Sidebar Button Style -->
        <Style x:Key="SidebarButton" TargetType="Button">
            <Setter Property="Height" Value="50"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Margin" Value="4,2"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Header Style -->
        <Style x:Key="HeaderText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>

        <!-- Statistic Card Style -->
        <Style x:Key="StatCard" TargetType="Border" BasedOn="{StaticResource ModernCard}">
            <Setter Property="Width" Value="200"/>
            <Setter Property="Height" Value="120"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Bar -->
        <Border Grid.Row="0" 
                Background="#1976D2"
                Height="70">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Ellipse Width="40" Height="40" Fill="White" VerticalAlignment="Center"/>
                    <TextBlock Text="نظام إدارة الشكاوى" 
                             FontSize="24" 
                             FontWeight="Bold" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             Margin="15,0,0,0"/>
                </StackPanel>

                <!-- Current Page Title -->
                <TextBlock Grid.Column="1" 
                         Text="الرئيسية" 
                         FontSize="18" 
                         FontWeight="SemiBold" 
                         Foreground="White" 
                         VerticalAlignment="Center" 
                         HorizontalAlignment="Center"/>

                <!-- User Info -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Ellipse Width="24" Height="24" Fill="White" VerticalAlignment="Center"/>
                    <TextBlock Text="المستخدم الحالي" 
                             FontSize="14" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             Margin="10,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <Border Grid.Column="0" 
                    Background="White"
                    BorderBrush="#E0E0E0"
                    BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="0,20">
                        <!-- Navigation Menu -->
                        <TextBlock Text="القائمة الرئيسية" 
                                 FontSize="16" 
                                 FontWeight="Bold" 
                                 Foreground="#666666" 
                                 Margin="20,0,20,15"/>

                        <Button Style="{StaticResource SidebarButton}" Click="NavigateHome_Click">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="الرئيسية" Margin="0,0,10,0"/>
                                <Ellipse Width="20" Height="20" Fill="#1976D2"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource SidebarButton}" Click="NavigateNewComplaint_Click">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="شكوى جديدة" Margin="0,0,10,0"/>
                                <Ellipse Width="20" Height="20" Fill="#4CAF50"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource SidebarButton}" Click="NavigateComplaints_Click">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="إدارة الشكاوى" Margin="0,0,10,0"/>
                                <Ellipse Width="20" Height="20" Fill="#FF9800"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource SidebarButton}" Click="NavigateIncoming_Click">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="الشكاوى الواردة" Margin="0,0,10,0"/>
                                <Ellipse Width="20" Height="20" Fill="#9C27B0"/>
                            </StackPanel>
                        </Button>

                        <Rectangle Height="1" Fill="#E0E0E0" Margin="20,15"/>

                        <TextBlock Text="التقارير والإحصائيات" 
                                 FontSize="16" 
                                 FontWeight="Bold" 
                                 Foreground="#666666" 
                                 Margin="20,15,20,15"/>

                        <Button Style="{StaticResource SidebarButton}" Click="NavigateReports_Click">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="التقارير" Margin="0,0,10,0"/>
                                <Ellipse Width="20" Height="20" Fill="#F44336"/>
                            </StackPanel>
                        </Button>

                        <Rectangle Height="1" Fill="#E0E0E0" Margin="20,15"/>

                        <Button Style="{StaticResource SidebarButton}" Click="NavigateSettings_Click">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="الإعدادات" Margin="0,0,10,0"/>
                                <Ellipse Width="20" Height="20" Fill="#607D8B"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Content Area -->
            <Grid Grid.Column="1" Margin="20">
                <ScrollViewer x:Name="MainContent" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- Welcome Section -->
                        <Border Style="{StaticResource ModernCard}" Margin="0,0,0,20">
                            <StackPanel>
                                <TextBlock Style="{StaticResource HeaderText}" Text="مرحباً بك في نظام إدارة الشكاوى"/>
                                <TextBlock Text="نظام متكامل لإدارة ومتابعة الشكاوى بكفاءة وفعالية" 
                                         FontSize="16" 
                                         Foreground="#666666" 
                                         Margin="0,0,0,20"/>
                                
                                <Button Background="#4CAF50" 
                                        Foreground="White"
                                        Padding="20,10"
                                        BorderThickness="0"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        HorizontalAlignment="Right"
                                        Click="NavigateNewComplaint_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="إضافة شكوى جديدة" Margin="0,0,8,0"/>
                                        <Ellipse Width="16" Height="16" Fill="White"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Border>

                        <!-- Statistics Cards -->
                        <TextBlock Text="الإحصائيات السريعة" 
                                 Style="{StaticResource HeaderText}" 
                                 Margin="0,0,0,20"/>
                        
                        <UniformGrid Columns="4" Margin="0,0,0,30">
                            <!-- Total Complaints -->
                            <Border Style="{StaticResource StatCard}" Background="#E3F2FD">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Ellipse Width="32" Height="32" Fill="#1976D2" HorizontalAlignment="Center"/>
                                    <TextBlock Text="125" 
                                             FontSize="28" 
                                             FontWeight="Bold" 
                                             Foreground="#1976D2" 
                                             HorizontalAlignment="Center" 
                                             Margin="0,8,0,4"/>
                                    <TextBlock Text="إجمالي الشكاوى" 
                                             FontSize="12" 
                                             Foreground="#666666" 
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- New Complaints -->
                            <Border Style="{StaticResource StatCard}" Background="#FFF3E0">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Ellipse Width="32" Height="32" Fill="#FF9800" HorizontalAlignment="Center"/>
                                    <TextBlock Text="23" 
                                             FontSize="28" 
                                             FontWeight="Bold" 
                                             Foreground="#FF9800" 
                                             HorizontalAlignment="Center" 
                                             Margin="0,8,0,4"/>
                                    <TextBlock Text="شكاوى جديدة" 
                                             FontSize="12" 
                                             Foreground="#666666" 
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- In Progress -->
                            <Border Style="{StaticResource StatCard}" Background="#F3E5F5">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Ellipse Width="32" Height="32" Fill="#9C27B0" HorizontalAlignment="Center"/>
                                    <TextBlock Text="67" 
                                             FontSize="28" 
                                             FontWeight="Bold" 
                                             Foreground="#9C27B0" 
                                             HorizontalAlignment="Center" 
                                             Margin="0,8,0,4"/>
                                    <TextBlock Text="قيد التنفيذ" 
                                             FontSize="12" 
                                             Foreground="#666666" 
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- Completed -->
                            <Border Style="{StaticResource StatCard}" Background="#E8F5E8">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Ellipse Width="32" Height="32" Fill="#4CAF50" HorizontalAlignment="Center"/>
                                    <TextBlock Text="35" 
                                             FontSize="28" 
                                             FontWeight="Bold" 
                                             Foreground="#4CAF50" 
                                             HorizontalAlignment="Center" 
                                             Margin="0,8,0,4"/>
                                    <TextBlock Text="مكتملة" 
                                             FontSize="12" 
                                             Foreground="#666666" 
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>

                        <!-- Recent Complaints -->
                        <Border Style="{StaticResource ModernCard}">
                            <StackPanel>
                                <Grid Margin="0,0,0,20">
                                    <TextBlock Text="الشكاوى الحديثة" Style="{StaticResource HeaderText}" Margin="0"/>
                                    <Button Background="#2196F3" 
                                            Foreground="White"
                                            Padding="10,5"
                                            BorderThickness="0"
                                            HorizontalAlignment="Left">
                                        <TextBlock Text="تحديث"/>
                                    </Button>
                                </Grid>

                                <DataGrid x:Name="ComplaintsGrid"
                                        AutoGenerateColumns="False"
                                        CanUserAddRows="False"
                                        CanUserDeleteRows="False"
                                        IsReadOnly="True"
                                        GridLinesVisibility="Horizontal"
                                        HeadersVisibility="Column"
                                        SelectionMode="Single"
                                        Background="White"
                                        RowBackground="White"
                                        AlternatingRowBackground="#F9F9F9"
                                        FlowDirection="RightToLeft">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="رقم الشكوى" Width="120"/>
                                        <DataGridTextColumn Header="العنوان" Width="*"/>
                                        <DataGridTextColumn Header="المشتكي" Width="150"/>
                                        <DataGridTextColumn Header="الحالة" Width="100"/>
                                        <DataGridTextColumn Header="التاريخ" Width="100"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Grid>
</Window>
