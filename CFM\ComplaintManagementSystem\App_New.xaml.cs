using System;
using System.Configuration;
using System.Data;
using System.IO;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using ComplaintManagementSystem.Data;
using ComplaintManagementSystem.Services;
using ComplaintManagementSystem.ViewModels;

namespace ComplaintManagementSystem;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private ServiceProvider? _serviceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // إعداد خدمات التطبيق
        var services = new ServiceCollection();
        ConfigureServices(services);
        _serviceProvider = services.BuildServiceProvider();

        // تهيئة قاعدة البيانات
        InitializeDatabase();

        // إظهار النافذة الرئيسية
        var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }

    private void ConfigureServices(ServiceCollection services)
    {
        // إعداد قاعدة البيانات
        services.AddDbContext<ComplaintDbContext>(options =>
            options.UseSqlServer(GetConnectionString()));

        // تسجيل الخدمات
        services.AddScoped<DatabaseService>();
        services.AddScoped<ComplaintService>();
        services.AddScoped<UserService>();
        services.AddScoped<LookupService>();

        // تسجيل ViewModels
        services.AddTransient<MainWindowViewModel>();

        // تسجيل النوافذ
        services.AddTransient<MainWindow>();
    }

    private string GetConnectionString()
    {
        // يمكن تخصيص سلسلة الاتصال حسب الحاجة
        var dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "ComplaintSystem", "Data");
        Directory.CreateDirectory(dataDirectory);
        
        var databasePath = Path.Combine(dataDirectory, "ComplaintSystem.mdf");
        
        return $@"Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename={databasePath};Integrated Security=True;Connect Timeout=30";
    }

    private async void InitializeDatabase()
    {
        try
        {
            using var scope = _serviceProvider!.CreateScope();
            var databaseService = scope.ServiceProvider.GetRequiredService<DatabaseService>();
            await databaseService.InitializeDatabaseAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _serviceProvider?.Dispose();
        base.OnExit(e);
    }

    public static T GetService<T>() where T : class
    {
        return ((App)Current)._serviceProvider!.GetRequiredService<T>();
    }
}
