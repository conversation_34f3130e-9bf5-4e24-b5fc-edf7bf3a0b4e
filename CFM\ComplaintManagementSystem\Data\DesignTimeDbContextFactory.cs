using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace ComplaintManagementSystem.Data
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<ComplaintDbContext>
    {
        public ComplaintDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<ComplaintDbContext>();

            // استخدام سلسلة اتصال مؤقتة للتصميم
            var connectionString = @"Data Source=(LocalDB)\MSSQLLocalDB;Initial Catalog=ComplaintManagementSystem;Integrated Security=True;Connect Timeout=30";

            optionsBuilder.UseSqlServer(connectionString);

            return new ComplaintDbContext(optionsBuilder.Options);
        }
    }
}
