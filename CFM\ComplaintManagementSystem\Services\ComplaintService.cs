using Microsoft.EntityFrameworkCore;
using ComplaintManagementSystem.Data;
using ComplaintManagementSystem.Models;

namespace ComplaintManagementSystem.Services
{
    public class ComplaintService
    {
        private readonly ComplaintDbContext _context;

        public ComplaintService(ComplaintDbContext context)
        {
            _context = context;
        }

        // إضافة شكوى جديدة
        public async Task<Complaint> AddComplaintAsync(Complaint complaint)
        {
            complaint.ComplaintNumber = await GenerateComplaintNumberAsync();
            complaint.CreatedDate = DateTime.Now;
            
            _context.Complaints.Add(complaint);
            await _context.SaveChangesAsync();

            // إضافة سجل في التاريخ
            await AddHistoryAsync(complaint.Id, "تم إنشاء الشكوى", "تم إنشاء شكوى جديدة", complaint.CreatedByUserId);

            return complaint;
        }

        // الحصول على جميع الشكاوى
        public async Task<List<Complaint>> GetAllComplaintsAsync()
        {
            return await _context.Complaints
                .Include(c => c.Status)
                .Include(c => c.Category)
                .Include(c => c.Priority)
                .Include(c => c.AssignedToUser)
                .Include(c => c.CreatedByUser)
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync();
        }

        // الحصول على شكوى بالمعرف
        public async Task<Complaint?> GetComplaintByIdAsync(int id)
        {
            return await _context.Complaints
                .Include(c => c.Status)
                .Include(c => c.Category)
                .Include(c => c.Priority)
                .Include(c => c.AssignedToUser)
                .Include(c => c.CreatedByUser)
                .Include(c => c.History).ThenInclude(h => h.ActionByUser)
                .Include(c => c.Comments).ThenInclude(c => c.CreatedByUser)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        // تحديث شكوى
        public async Task<Complaint> UpdateComplaintAsync(Complaint complaint)
        {
            var existingComplaint = await _context.Complaints.FindAsync(complaint.Id);
            if (existingComplaint == null)
                throw new ArgumentException("الشكوى غير موجودة");

            // تتبع التغييرات
            var oldStatusId = existingComplaint.StatusId;
            var oldAssignedToUserId = existingComplaint.AssignedToUserId;

            existingComplaint.Title = complaint.Title;
            existingComplaint.Description = complaint.Description;
            existingComplaint.StatusId = complaint.StatusId;
            existingComplaint.CategoryId = complaint.CategoryId;
            existingComplaint.PriorityId = complaint.PriorityId;
            existingComplaint.AssignedToUserId = complaint.AssignedToUserId;
            existingComplaint.Resolution = complaint.Resolution;
            existingComplaint.LastModifiedDate = DateTime.Now;

            if (complaint.StatusId == 4) // مكتملة
            {
                existingComplaint.ResolvedDate = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            // إضافة سجل في التاريخ للتغييرات
            if (oldStatusId != complaint.StatusId || oldAssignedToUserId != complaint.AssignedToUserId)
            {
                await AddHistoryAsync(complaint.Id, "تم تحديث الشكوى", "تم تحديث بيانات الشكوى", 
                    complaint.CreatedByUserId, oldStatusId, complaint.StatusId, 
                    oldAssignedToUserId, complaint.AssignedToUserId);
            }

            return existingComplaint;
        }

        // حذف شكوى
        public async Task DeleteComplaintAsync(int id)
        {
            var complaint = await _context.Complaints.FindAsync(id);
            if (complaint != null)
            {
                _context.Complaints.Remove(complaint);
                await _context.SaveChangesAsync();
            }
        }

        // البحث في الشكاوى
        public async Task<List<Complaint>> SearchComplaintsAsync(string searchTerm, int? statusId = null, int? categoryId = null)
        {
            var query = _context.Complaints
                .Include(c => c.Status)
                .Include(c => c.Category)
                .Include(c => c.Priority)
                .Include(c => c.AssignedToUser)
                .Include(c => c.CreatedByUser)
                .AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(c => c.Title.Contains(searchTerm) || 
                                        c.Description.Contains(searchTerm) ||
                                        c.ComplainerName.Contains(searchTerm) ||
                                        c.ComplaintNumber.Contains(searchTerm));
            }

            if (statusId.HasValue)
            {
                query = query.Where(c => c.StatusId == statusId.Value);
            }

            if (categoryId.HasValue)
            {
                query = query.Where(c => c.CategoryId == categoryId.Value);
            }

            return await query.OrderByDescending(c => c.CreatedDate).ToListAsync();
        }

        // إضافة تعليق
        public async Task<ComplaintComment> AddCommentAsync(ComplaintComment comment)
        {
            comment.CreatedDate = DateTime.Now;
            _context.ComplaintComments.Add(comment);
            await _context.SaveChangesAsync();

            await AddHistoryAsync(comment.ComplaintId, "تم إضافة تعليق", 
                comment.IsInternal ? "تم إضافة تعليق داخلي" : "تم إضافة تعليق", 
                comment.CreatedByUserId);

            return comment;
        }

        // إضافة سجل في التاريخ
        private async Task AddHistoryAsync(int complaintId, string action, string description, int userId,
            int? oldStatusId = null, int? newStatusId = null, 
            int? oldAssignedToUserId = null, int? newAssignedToUserId = null)
        {
            var history = new ComplaintHistory
            {
                ComplaintId = complaintId,
                Action = action,
                Description = description,
                ActionDate = DateTime.Now,
                ActionByUserId = userId,
                OldStatusId = oldStatusId,
                NewStatusId = newStatusId,
                OldAssignedToUserId = oldAssignedToUserId,
                NewAssignedToUserId = newAssignedToUserId
            };

            _context.ComplaintHistories.Add(history);
            await _context.SaveChangesAsync();
        }

        // توليد رقم شكوى
        private async Task<string> GenerateComplaintNumberAsync()
        {
            var year = DateTime.Now.Year;
            var count = await _context.Complaints.CountAsync(c => c.CreatedDate.Year == year);
            return $"C{year}{(count + 1):D6}";
        }

        // الحصول على إحصائيات
        public async Task<Dictionary<string, int>> GetComplaintStatisticsAsync()
        {
            var stats = new Dictionary<string, int>();

            stats["Total"] = await _context.Complaints.CountAsync();
            stats["New"] = await _context.Complaints.CountAsync(c => c.StatusId == 1);
            stats["InReview"] = await _context.Complaints.CountAsync(c => c.StatusId == 2);
            stats["InProgress"] = await _context.Complaints.CountAsync(c => c.StatusId == 3);
            stats["Completed"] = await _context.Complaints.CountAsync(c => c.StatusId == 4);
            stats["Rejected"] = await _context.Complaints.CountAsync(c => c.StatusId == 5);

            return stats;
        }
    }
}
