using System.Windows;
using System.Windows.Controls;
using System.Collections.ObjectModel;

namespace ComplaintManagementSystem
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public ObservableCollection<ComplaintItem> RecentComplaints { get; set; } = new();

        public MainWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            // إضافة بيانات تجريبية للعرض
            RecentComplaints = new ObservableCollection<ComplaintItem>
            {
                new ComplaintItem { ComplaintNumber = "C2025000001", Title = "مشكلة في الخدمة", ComplainerName = "أحمد محمد", Status = "جديدة", Date = "2025/01/15" },
                new ComplaintItem { ComplaintNumber = "C2025000002", Title = "تأخير في الرد", ComplainerName = "فاطمة علي", Status = "قيد المراجعة", Date = "2025/01/14" },
                new ComplaintItem { ComplaintNumber = "C2025000003", Title = "عدم وضوح المعلومات", ComplainerName = "محمد أحمد", Status = "مكتملة", Date = "2025/01/13" },
                new ComplaintItem { ComplaintNumber = "C2025000004", Title = "طلب تحسين الخدمة", ComplainerName = "سارة محمود", Status = "قيد التنفيذ", Date = "2025/01/12" },
                new ComplaintItem { ComplaintNumber = "C2025000005", Title = "استفسار عن الإجراءات", ComplainerName = "عبدالله سالم", Status = "جديدة", Date = "2025/01/11" }
            };

            ComplaintsGrid.ItemsSource = RecentComplaints;
        }

        private void NavigateHome_Click(object sender, RoutedEventArgs e)
        {
            // عرض الصفحة الرئيسية
            MessageBox.Show("تم الانتقال إلى الصفحة الرئيسية", "التنقل", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NavigateNewComplaint_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة شكوى جديدة
            var newComplaintWindow = new NewComplaintWindow();
            newComplaintWindow.ShowDialog();
        }

        private void NavigateComplaints_Click(object sender, RoutedEventArgs e)
        {
            // عرض قائمة الشكاوى
            MessageBox.Show("سيتم فتح صفحة إدارة الشكاوى", "التنقل", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NavigateIncoming_Click(object sender, RoutedEventArgs e)
        {
            // عرض الشكاوى الواردة
            MessageBox.Show("سيتم فتح صفحة الشكاوى الواردة", "التنقل", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NavigateReports_Click(object sender, RoutedEventArgs e)
        {
            // عرض التقارير
            MessageBox.Show("سيتم فتح صفحة التقارير والإحصائيات", "التنقل", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NavigateSettings_Click(object sender, RoutedEventArgs e)
        {
            // عرض الإعدادات
            MessageBox.Show("سيتم فتح صفحة الإعدادات", "التنقل", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // فئة مساعدة لعرض بيانات الشكاوى
    public class ComplaintItem
    {
        public string ComplaintNumber { get; set; } = "";
        public string Title { get; set; } = "";
        public string ComplainerName { get; set; } = "";
        public string Status { get; set; } = "";
        public string Date { get; set; } = "";
    }
}
