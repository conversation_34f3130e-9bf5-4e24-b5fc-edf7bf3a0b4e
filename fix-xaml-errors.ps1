# إصلاح أخطاء XAML في مشروع ComplaintManagementSystem

# إصلاح ReportsPage.xaml
$reportsPagePath = "ComplaintManagementSystem\Views\ReportsPage.xaml"
if (Test-Path $reportsPagePath) {
    $content = Get-Content $reportsPagePath -Raw
    
    # إزالة Content attribute عندما يوجد Button.Content
    $content = $content -replace 'Content="[^"]*"(\s+[^>]*>)\s*<Button\.Content>', '$1<Button.Content>'
    
    # إصلاح الأزرار المكررة
    $content = $content -replace '<Button\.Content>\s*<StackPanel[^>]*>\s*<materialDesign:PackIcon[^>]*\/>\s*<TextBlock[^>]*\/>\s*<\/StackPanel>\s*<\/Button\.Content>', ''
    
    Set-Content $reportsPagePath $content -Encoding UTF8
    Write-Host "تم إصلاح ReportsPage.xaml" -ForegroundColor Green
}

# إصلاح SettingsPage.xaml
$settingsPagePath = "ComplaintManagementSystem\Views\SettingsPage.xaml"
if (Test-Path $settingsPagePath) {
    $content = Get-Content $settingsPagePath -Raw
    
    # إزالة Content attribute عندما يوجد Button.Content
    $content = $content -replace 'Content="[^"]*"(\s+[^>]*>)\s*<Button\.Content>', '$1<Button.Content>'
    
    Set-Content $settingsPagePath $content -Encoding UTF8
    Write-Host "تم إصلاح SettingsPage.xaml" -ForegroundColor Green
}

# إصلاح ViewComplaintsPage.xaml
$viewComplaintsPagePath = "ComplaintManagementSystem\Views\ViewComplaintsPage.xaml"
if (Test-Path $viewComplaintsPagePath) {
    $content = Get-Content $viewComplaintsPagePath -Raw
    
    # إزالة Content attribute عندما يوجد Button.Content
    $content = $content -replace 'Content="[^"]*"(\s+[^>]*>)\s*<Button\.Content>', '$1<Button.Content>'
    
    Set-Content $viewComplaintsPagePath $content -Encoding UTF8
    Write-Host "تم إصلاح ViewComplaintsPage.xaml" -ForegroundColor Green
}

Write-Host "تم الانتهاء من إصلاح جميع ملفات XAML" -ForegroundColor Yellow
