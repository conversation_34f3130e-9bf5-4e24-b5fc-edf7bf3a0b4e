using System;
using System.Windows;
using Microsoft.Win32;

namespace ComplaintManagementSystem
{
    /// <summary>
    /// Interaction logic for NewComplaintWindow.xaml
    /// </summary>
    public partial class NewComplaintWindow : Window
    {
        public NewComplaintWindow()
        {
            InitializeComponent();
            
            // تعيين التاريخ الحالي + 7 أيام كتاريخ متوقع للحل
            ExpectedDatePicker.SelectedDate = DateTime.Now.AddDays(7);
        }

        private void BrowseFiles_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "اختيار الملفات المرفقة",
                Filter = "جميع الملفات (*.*)|*.*|ملفات الصور (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif|ملفات PDF (*.pdf)|*.pdf|ملفات Word (*.doc;*.docx)|*.doc;*.docx",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                string fileNames = string.Join("; ", openFileDialog.FileNames);
                AttachmentTextBox.Text = fileNames;
            }
        }

        private void SaveComplaint_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                // هنا سيتم حفظ الشكوى في قاعدة البيانات
                MessageBox.Show("تم حفظ الشكوى بنجاح!\nرقم الشكوى: C2025000006", 
                              "نجح الحفظ", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Information);
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private void SaveAndNew_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                // حفظ الشكوى الحالية
                MessageBox.Show("تم حفظ الشكوى بنجاح!\nرقم الشكوى: C2025000006", 
                              "نجح الحفظ", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Information);
                
                // مسح النموذج للشكوى الجديدة
                ClearForm();
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إلغاء إدخال الشكوى؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                       "تأكيد الإلغاء", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private bool ValidateForm()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان الشكوى", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                TitleTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ComplainerNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المشتكي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ComplainerNameTextBox.Focus();
                return false;
            }

            if (CategoryComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار تصنيف الشكوى", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryComboBox.Focus();
                return false;
            }

            if (PriorityComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار أولوية الشكوى", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PriorityComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال وصف الشكوى", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                DescriptionTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            TitleTextBox.Text = "";
            ComplainerNameTextBox.Text = "";
            PhoneTextBox.Text = "";
            EmailTextBox.Text = "";
            IdNumberTextBox.Text = "";
            CategoryComboBox.SelectedIndex = -1;
            PriorityComboBox.SelectedIndex = -1;
            DescriptionTextBox.Text = "";
            AttachmentTextBox.Text = "لا توجد ملفات مرفقة";
            ExpectedDatePicker.SelectedDate = DateTime.Now.AddDays(7);
            
            // التركيز على أول حقل
            TitleTextBox.Focus();
        }
    }
}
