using System.ComponentModel.DataAnnotations;

namespace ComplaintManagementSystem.Models
{
    public class User
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Email { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Phone { get; set; }

        [Required]
        [StringLength(100)]
        public string Department { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Position { get; set; } = string.Empty;

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastLoginDate { get; set; }

        [Required]
        public UserRole Role { get; set; } = UserRole.Employee;

        public virtual ICollection<Complaint> CreatedComplaints { get; set; } = new List<Complaint>();
        public virtual ICollection<Complaint> AssignedComplaints { get; set; } = new List<Complaint>();
        public virtual ICollection<ComplaintHistory> ComplaintHistories { get; set; } = new List<ComplaintHistory>();
        public virtual ICollection<ComplaintComment> Comments { get; set; } = new List<ComplaintComment>();
    }

    public enum UserRole
    {
        Admin = 1,
        Manager = 2,
        Employee = 3,
        Viewer = 4
    }
}
