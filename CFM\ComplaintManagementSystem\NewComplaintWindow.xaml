<Window x:Class="ComplaintManagementSystem.NewComplaintWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="شكوى جديدة" 
        Height="700" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Input Style -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="ModernLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <!-- Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1976D2" Height="60">
            <Grid Margin="20,0">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <Ellipse Width="30" Height="30" Fill="White" VerticalAlignment="Center"/>
                    <TextBlock Text="إضافة شكوى جديدة" 
                             FontSize="20" 
                             FontWeight="Bold" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             Margin="15,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20">
            <Border Style="{StaticResource ModernCard}">
                <StackPanel>
                    <TextBlock Text="معلومات الشكوى" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Foreground="#1976D2" 
                             Margin="0,0,0,20"/>

                    <!-- Complaint Title -->
                    <TextBlock Text="عنوان الشكوى *" Style="{StaticResource ModernLabel}"/>
                    <TextBox x:Name="TitleTextBox" 
                           Style="{StaticResource ModernTextBox}"
                           Text="مشكلة في الخدمة المقدمة"/>

                    <!-- Complainer Information -->
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="اسم المشتكي *" Style="{StaticResource ModernLabel}"/>
                            <TextBox x:Name="ComplainerNameTextBox" 
                                   Style="{StaticResource ModernTextBox}"
                                   Text="أحمد محمد علي"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="رقم الهاتف" Style="{StaticResource ModernLabel}"/>
                            <TextBox x:Name="PhoneTextBox" 
                                   Style="{StaticResource ModernTextBox}"
                                   Text="0501234567"/>
                        </StackPanel>
                    </Grid>

                    <!-- Email and ID -->
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="البريد الإلكتروني" Style="{StaticResource ModernLabel}"/>
                            <TextBox x:Name="EmailTextBox" 
                                   Style="{StaticResource ModernTextBox}"
                                   Text="<EMAIL>"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="رقم الهوية" Style="{StaticResource ModernLabel}"/>
                            <TextBox x:Name="IdNumberTextBox" 
                                   Style="{StaticResource ModernTextBox}"
                                   Text="1234567890"/>
                        </StackPanel>
                    </Grid>

                    <!-- Category and Priority -->
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="تصنيف الشكوى *" Style="{StaticResource ModernLabel}"/>
                            <ComboBox x:Name="CategoryComboBox" 
                                    Height="35" 
                                    FontSize="14"
                                    Margin="0,5">
                                <ComboBoxItem Content="خدمة العملاء" IsSelected="True"/>
                                <ComboBoxItem Content="الجودة"/>
                                <ComboBoxItem Content="التسليم"/>
                                <ComboBoxItem Content="الفواتير"/>
                                <ComboBoxItem Content="أخرى"/>
                            </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="الأولوية *" Style="{StaticResource ModernLabel}"/>
                            <ComboBox x:Name="PriorityComboBox" 
                                    Height="35" 
                                    FontSize="14"
                                    Margin="0,5">
                                <ComboBoxItem Content="منخفضة"/>
                                <ComboBoxItem Content="متوسطة" IsSelected="True"/>
                                <ComboBoxItem Content="عالية"/>
                                <ComboBoxItem Content="عاجلة"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- Description -->
                    <TextBlock Text="وصف الشكوى *" Style="{StaticResource ModernLabel}" Margin="0,20,0,5"/>
                    <TextBox x:Name="DescriptionTextBox" 
                           Style="{StaticResource ModernTextBox}"
                           Height="120" 
                           TextWrapping="Wrap" 
                           AcceptsReturn="True"
                           VerticalScrollBarVisibility="Auto"
                           Text="أواجه مشكلة في الخدمة المقدمة حيث أن الاستجابة بطيئة جداً ولا تلبي التوقعات المطلوبة. أرجو النظر في هذا الأمر وتحسين مستوى الخدمة."/>

                    <!-- Attachments -->
                    <TextBlock Text="المرفقات" Style="{StaticResource ModernLabel}" Margin="0,20,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="AttachmentTextBox" 
                               Grid.Column="0"
                               Style="{StaticResource ModernTextBox}"
                               IsReadOnly="True"
                               Text="لا توجد ملفات مرفقة"/>
                        
                        <Button Grid.Column="1" 
                              Background="#4CAF50" 
                              Foreground="White"
                              Style="{StaticResource ModernButton}"
                              Click="BrowseFiles_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="استعراض" Margin="0,0,5,0"/>
                                <Ellipse Width="16" Height="16" Fill="White"/>
                            </StackPanel>
                        </Button>
                    </Grid>

                    <!-- Expected Resolution Date -->
                    <TextBlock Text="التاريخ المتوقع للحل" Style="{StaticResource ModernLabel}" Margin="0,20,0,5"/>
                    <DatePicker x:Name="ExpectedDatePicker" 
                              Height="35" 
                              FontSize="14"
                              Margin="0,5"
                              SelectedDate="{x:Static sys:DateTime.Now}"
                              xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" 
                      HorizontalAlignment="Left" 
                      Margin="20,15"
                      FlowDirection="LeftToRight">
                
                <Button Background="#4CAF50" 
                      Foreground="White"
                      Style="{StaticResource ModernButton}"
                      Click="SaveComplaint_Click">
                    <StackPanel Orientation="Horizontal">
                        <Ellipse Width="16" Height="16" Fill="White" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الشكوى"/>
                    </StackPanel>
                </Button>

                <Button Background="#2196F3" 
                      Foreground="White"
                      Style="{StaticResource ModernButton}"
                      Click="SaveAndNew_Click">
                    <StackPanel Orientation="Horizontal">
                        <Ellipse Width="16" Height="16" Fill="White" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإضافة جديدة"/>
                    </StackPanel>
                </Button>

                <Button Background="#757575" 
                      Foreground="White"
                      Style="{StaticResource ModernButton}"
                      Click="Cancel_Click">
                    <StackPanel Orientation="Horizontal">
                        <Ellipse Width="16" Height="16" Fill="White" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
